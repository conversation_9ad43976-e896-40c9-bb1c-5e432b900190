-- Fresh database setup script for ODude CRM platform
-- This script creates the required tables for user profiles and settings
--
-- Features included:
-- - User profiles management
-- - User-specific settings and configurations
-- - Proper indexing for performance

-- Create profiles table if it doesn't exist
CREATE TABLE IF NOT EXISTS profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  full_name TEXT,
  avatar_url TEXT,
  email TEXT UNIQUE,
  disabled BOOLEAN DEFAULT FALSE
);

-- Add indexes for profiles table
CREATE INDEX IF NOT EXISTS idx_profiles_email ON profiles (email);
CREATE INDEX IF NOT EXISTS idx_profiles_created_at ON profiles (created_at);

-- Add comments for profiles table
COMMENT ON TABLE profiles IS 'User profiles with basic information';
COMMENT ON COLUMN profiles.id IS 'Unique identifier for the profile';
COMMENT ON COLUMN profiles.email IS 'User email address (unique)';
COMMENT ON COLUMN profiles.full_name IS 'User full name';
COMMENT ON COLUMN profiles.avatar_url IS 'URL to user avatar image';
COMMENT ON COLUMN profiles.disabled IS 'Whether the user account is disabled';
COMMENT ON COLUMN profiles.created_at IS 'When the profile was created';
COMMENT ON COLUMN profiles.updated_at IS 'When the profile was last updated';

-- Create settings table for user-specific configurations
CREATE TABLE IF NOT EXISTS settings (
  email TEXT PRIMARY KEY,
  max_contact_limit INTEGER DEFAULT 4,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add index for settings table
CREATE INDEX IF NOT EXISTS idx_settings_email ON settings (email);

-- Add comments for settings table
COMMENT ON TABLE settings IS 'User-specific settings and configurations';
COMMENT ON COLUMN settings.email IS 'User email address (primary key)';
COMMENT ON COLUMN settings.max_contact_limit IS 'Maximum number of contacts allowed for this user';
COMMENT ON COLUMN settings.created_at IS 'When the settings record was created';
COMMENT ON COLUMN settings.updated_at IS 'When the settings record was last updated';

-- Create trigger functions for updating timestamps
CREATE OR REPLACE FUNCTION update_profiles_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_settings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to update updated_at timestamp
CREATE TRIGGER trigger_update_profiles_updated_at
    BEFORE UPDATE ON profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_profiles_updated_at();

CREATE TRIGGER trigger_update_settings_updated_at
    BEFORE UPDATE ON settings
    FOR EACH ROW
    EXECUTE FUNCTION update_settings_updated_at();

-- Verification queries
SELECT 'Profiles and settings tables created successfully' as status;
SELECT COUNT(*) as total_profiles FROM profiles;
SELECT COUNT(*) as total_settings FROM settings;

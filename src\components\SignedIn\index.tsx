"use client"

import { useSession } from "next-auth/react"
import { Title, Text, Stack, Paper, Group, Avatar } from '@mantine/core';
import { AdSenseBanner } from "../AdSense";


export const SignedIn = () => {
  const { data: session } = useSession();

  return (
    <Stack gap="lg" p="md">
      <Paper withBorder p="lg" radius="md">
        <Group>
          <Avatar
            src={session?.user?.image}
            size="lg"
            radius="xl"
          />
          <div>
            <Title order={2}>Welcome back!</Title>
            <Text c="dimmed">{session?.user?.name}</Text>
            <Text size="sm" c="dimmed">{session?.user?.email}</Text>
          </div>
        </Group>
      </Paper>

      <Paper withBorder p="lg" radius="md">
        <Title order={3} mb="md">Dashboard</Title>
        <Text>
          Welcome to ODude CRM! This is your simplified dashboard where you can manage your profile and settings.
        </Text>
      </Paper>

      <AdSenseBanner slot="1234567890" />
    </Stack>
  );
}



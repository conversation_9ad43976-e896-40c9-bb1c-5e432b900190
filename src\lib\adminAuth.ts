import { NextRequest } from 'next/server';
import { auth } from 'auth';
import { ADMIN_EMAIL } from './config';
import { getSupabaseClient } from './supabase';

export interface AdminAuthResult {
  isAuthorized: boolean;
  isSuperAdmin: boolean;
  session: any;
  error: string | null;
  status: number;
}

/**
 * Server-side admin authentication check
 * This function checks both the user session and admin email
 * Only super admin access is allowed
 */
export async function verifyAdminAuth(request?: NextRequest): Promise<AdminAuthResult> {
  try {
    // Get the current session
    const session = await auth();

    if (!session?.user?.email) {
      return {
        isAuthorized: false,
        isSuperAdmin: false,
        session: null,
        error: 'No authenticated session',
        status: 401
      };
    }

    const userEmail = session.user.email;

    // Check if user is super admin
    const isSuperAdmin = userEmail === ADMIN_EMAIL;

    // User must be super admin
    if (!isSuperAdmin) {
      return {
        isAuthorized: false,
        isSuperAdmin: false,
        session,
        error: 'Unauthorized - super admin access required',
        status: 403
      };
    }

    return {
      isAuthorized: true,
      isSuperAdmin,
      session,
      error: null,
      status: 200
    };
  } catch (error) {
    console.error('Admin auth verification error:', error);
    return {
      isAuthorized: false,
      isSuperAdmin: false,
      session: null,
      error: 'Authentication failed',
      status: 500
    };
  }
}

/**
 * Middleware function to protect admin API routes
 * Usage: const authResult = await requireAdminAuth();
 * if (!authResult.isAuthorized) return NextResponse.json({ error: authResult.error }, { status: authResult.status });
 */
export async function requireAdminAuth(request?: NextRequest): Promise<AdminAuthResult> {
  return await verifyAdminAuth(request);
}
